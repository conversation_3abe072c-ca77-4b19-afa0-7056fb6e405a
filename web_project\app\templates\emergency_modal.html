<!-- 紧急呼叫模态窗口 -->
<div class="modal fade" id="emergencyModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h4 class="modal-title"><i class="bi bi-exclamation-triangle-fill me-2"></i> 紧急呼叫</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div v-if="!emergencyStatus">
                    <div class="p-4">
                        <h5 class="mb-3">请选择紧急情况类型：</h5>
                        <div class="d-flex justify-content-between gap-3">
                            <div class="emergency-option medical text-center p-4 flex-fill" @click="emergencyType = 'medical'; startEmergencyCall()">
                                <i class="bi bi-heart-pulse fs-1 mb-2"></i>
                                <div>医疗紧急</div>
                            </div>
                            <div class="emergency-option fall text-center p-4 flex-fill" @click="emergencyType = 'fall'; startEmergencyCall()">
                                <img src="/static/images/fall.png" class="fs-1 mb-2" width="60" height="60">
                                <div>跌倒求助</div>
                            </div>
                            <div class="emergency-option help text-center p-4 flex-fill" @click="emergencyType = 'help'; startEmergencyCall()">
                                <i class="bi bi-megaphone-fill fs-1 mb-2"></i>
                                <div>其他求助</div>
                            </div>
                        </div>

                        <div class="alert alert-warning mt-4">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            请注意：点击紧急情况类型后，系统将自动拨打相应的紧急联系人。如非必要，请勿点击。
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    </div>
                </div>

                <div v-else>
                    <div class="text-center p-4">
                        <div v-if="emergencyStatus === 'calling'">
                            <!-- 步骤2：等待响应 -->
                            <div class="text-center mb-4">
                                <div class="emergency-countdown mb-3">
                                    <span>${countdownTimer}$s</span>
                                </div>
                                <h5 class="mb-3 fs-3">正在等待家属或工作人员响应...</h5>
                                <p class="text-muted fs-5">紧急呼叫已发送，请耐心等待</p>
                                <div class="spinner-border text-danger mt-2" role="status">
                                    <span class="visually-hidden">等待中...</span>
                                </div>
                            </div>

                            <!-- 地图组件 -->
                            <div class="emergency-map-container" id="emergencyMap"></div>

                            <div class="location-info">
                                <i class="bi bi-geo-alt-fill"></i>
                                <div>
                                    <p class="mb-1 fs-5" v-if="currentLocation">您的当前位置：${currentLocation.address}$</p>
                                    <p class="mb-0 fs-5" v-else>正在获取您的位置...</p>
                                </div>
                            </div>

                            <!-- 状态进度条 -->
                            <div class="emergency-status-progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 70%"></div>
                            </div>

                            <div class="text-center mt-4">
                                <button class="btn btn-secondary btn-lg px-4 py-2 mx-2" @click="cancelEmergencyCall">
                                    <i class="bi bi-x-circle me-2"></i> 取消呼叫
                                </button>
                            </div>
                        </div>

                        <div v-else-if="emergencyStatus === 'connected'">
                            <!-- 步骤3：已连接 - 显示详细响应信息 -->
                            <div class="alert alert-success fs-5 mb-4">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                <strong>您的紧急求助已收到响应！</strong>
                            </div>

                            <!-- 响应者信息区域 -->
                            <div class="responders-section mb-4">
                                <h6 class="text-center mb-3 fs-5">
                                    <i class="bi bi-people-fill me-2"></i>响应人员信息
                                </h6>

                                <!-- 家属响应信息 -->
                                <div v-if="familyResponse" class="response-card family-response mb-3">
                                    <div class="response-header">
                                        <div class="responder-avatar family-avatar">
                                            <i class="bi bi-person-heart"></i>
                                        </div>
                                        <div class="responder-info">
                                            <h6 class="mb-1">👨‍👩‍👧‍👦 家属：${familyResponse.responder_name}$</h6>
                                            <small class="text-muted">响应时间：${familyResponse.response_time}$</small>
                                        </div>
                                        <div class="response-status">
                                            <span class="badge bg-success">已响应</span>
                                        </div>
                                    </div>
                                    <div class="response-message" v-if="familyResponse.message">
                                        <div class="message-bubble family-message">
                                            <i class="bi bi-chat-quote-fill me-2"></i>
                                            <span>${familyResponse.message}$</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 社区工作人员响应信息 -->
                                <div v-if="workerResponse" class="response-card worker-response mb-3">
                                    <div class="response-header">
                                        <div class="responder-avatar worker-avatar">
                                            <i class="bi bi-shield-check"></i>
                                        </div>
                                        <div class="responder-info">
                                            <h6 class="mb-1">👷‍♂️ 社区工作人员：${workerResponse.responder_name}$</h6>
                                            <small class="text-muted">响应时间：${workerResponse.response_time}$</small>
                                        </div>
                                        <div class="response-status">
                                            <span class="badge bg-info">已响应</span>
                                        </div>
                                    </div>
                                    <div class="response-message" v-if="workerResponse.message">
                                        <div class="message-bubble worker-message">
                                            <i class="bi bi-chat-quote-fill me-2"></i>
                                            <span>${workerResponse.message}$</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 如果没有详细回复，显示默认信息 -->
                                <div v-if="!familyResponse && !workerResponse" class="alert alert-info">
                                    <i class="bi bi-info-circle-fill me-2"></i>
                                    ${responderInfo}$，救援人员正在赶来
                                </div>
                            </div>

                            <!-- 救援状态信息 -->
                            <div class="rescue-status-section mb-4">
                                <div class="alert alert-warning">
                                    <i class="bi bi-clock-fill me-2"></i>
                                    <strong>救援状态：</strong>救援人员正在前往您的位置
                                </div>

                                <div class="rescue-timeline">
                                    <div class="timeline-item completed">
                                        <i class="bi bi-check-circle-fill"></i>
                                        <span>紧急呼叫已发送</span>
                                    </div>
                                    <div class="timeline-item completed">
                                        <i class="bi bi-check-circle-fill"></i>
                                        <span>救援人员已响应</span>
                                    </div>
                                    <div class="timeline-item active">
                                        <i class="bi bi-arrow-right-circle-fill"></i>
                                        <span>救援人员正在赶来</span>
                                    </div>
                                    <div class="timeline-item pending">
                                        <i class="bi bi-circle"></i>
                                        <span>预计5-10分钟到达</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 地图组件 -->
                            <div class="emergency-map-container mb-4" id="emergencyMap"></div>

                            <div class="location-info mb-4">
                                <i class="bi bi-geo-alt-fill text-danger"></i>
                                <div>
                                    <p class="mb-1 fs-6" v-if="currentLocation">
                                        <strong>您的位置：</strong>${currentLocation.address}$
                                    </p>
                                    <p class="mb-0 text-success fs-6">
                                        <strong>预计到达时间：</strong>5-10分钟
                                    </p>
                                </div>
                            </div>

                            <!-- 紧急提醒 -->
                            <div class="alert alert-danger mb-4">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <strong>重要提醒：</strong>请保持冷静，留在原地等待救援。如情况紧急，请立即拨打120。
                            </div>

                            <div class="text-center">
                                <button class="btn btn-success btn-lg px-4 py-2 me-2" @click="confirmReceived">
                                    <i class="bi bi-check-circle me-2"></i> 确认收到
                                </button>
                                <button class="btn btn-outline-danger btn-lg px-4 py-2" @click="callEmergencyServices">
                                    <i class="bi bi-telephone me-2"></i> 拨打120
                                </button>
                            </div>
                        </div>

                        <div v-else>
                            <div class="alert alert-info mb-3">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                ${emergencyStatus}$
                            </div>

                            <div id="emergencyMap" style="height: 200px;" class="mb-3"></div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>